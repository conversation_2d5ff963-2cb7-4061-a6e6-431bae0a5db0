# Getting Support

Since osgEarth is a free open source SDK, the code is available to anyone and we welcome and encourage community participation when it comes to testing, adding features, and fixing bugs.

## GitHub Repository

Use the [osgEarth GitHub repository](https://github.com/gwaldron/osgearth) to access the source, download releases, and post bug reports.

## Discussion Forum

Join the [osgEarth Discussion Forum](https://github.com/gwaldron/osgearth/discussions) to interact with other users. Please read and follow these guidelines for using the forum. FOLLOWING THESE GUIDELINES will make it MUCH MORE LIKELY that someone will respond and try to help:

* Sign up for an account and use your real name. You can participate anonymously, but using your real name helps build a stronger community. Sign your posts too!
* Limit yourself to one topic per post. Asking multiple questions in one post makes it too hard to keep track of responses.
* Always include as much supporting information as possible. Post an earth file or short code snippet. Post the output to osgearth_version --caps. Post the output to gdalinfo if you are having trouble with a GeoTIFF or other data file. List everything you have tried so far.
* Be patient!

## OpenSceneGraph Support

osgEarth operates on top of OpenSceneGraph (OSG), an open source 3D rendering toolkit. If you are unable to find answers to your problems on GitHub or in the osgEarth Forum, the [OSG Google Group](https://groups.google.com/g/osg-users) is another place to look.

## Professional Services

The osgEarth team supports its efforts through professional services. At [Pelican Mapping](http://pelicanmapping.com) we do custom software development and integration work involving osgEarth (and geospatial technologies in general). We are based in the US but we work with clients all over the world. [Contact us if you need help!](http://web.pelicanmapping.com/contact-us/)
