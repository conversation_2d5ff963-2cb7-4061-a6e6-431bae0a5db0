Group {
  name "db"
  nodeMask 0xffffffff
  cullingActive TRUE
  num_children 1
  Group {
    name "Default"
    nodeMask 0xffffffff
    cullingActive TRUE
    num_children 2
    Geode {
      DataVariance STATIC
      name "p21"
      nodeMask 0xffffffff
      cullingActive TRUE
      StateSet {
        DataVariance STATIC
        rendering_hint DEFAULT_BIN
        renderBinMode INHERIT
        GL_CULL_FACE ON
        CullFace {
          mode BACK
        }
      }
      num_drawables 1
      Geometry {
        DataVariance STATIC
        supportsDisplayList FALSE
        useDisplayList FALSE
        useVertexBufferObjects TRUE
        PrimitiveSets 1
        {
          DrawElementsUShort TRIANGLES 66
          {
            0 1 2 0 2 3 4 5 6 4
            6 7 8 9 10 8 10 11 12 13
            14 12 14 15 16 17 18 16 18 19
            20 21 22 20 22 23 24 25 26 24
            26 27 28 29 30 28 30 31 32 33
            34 32 34 35 32 35 36 32 36 37
            32 37 38 32 38 39 
          }
        }
        VertexArray Vec3Array 40
        {
          -0.0353553 -0.0353553 0
          -0.0353553 -0.0353553 1
          -0.05 0 1
          -0.05 0 0
          -3.06152e-018 -0.05 0
          -3.06152e-018 -0.05 1
          -0.0353553 -0.0353553 1
          -0.0353553 -0.0353553 0
          0.0353553 -0.0353553 0
          0.0353553 -0.0353553 1
          -3.06152e-018 -0.05 1
          -3.06152e-018 -0.05 0
          0.05 -6.12303e-018 0
          0.05 -6.12303e-018 1
          0.0353553 -0.0353553 1
          0.0353553 -0.0353553 0
          0.0353553 0.0353553 0
          0.0353553 0.0353553 1
          0.05 -6.12303e-018 1
          0.05 -6.12303e-018 0
          9.18455e-018 0.05 0
          9.18455e-018 0.05 1
          0.0353553 0.0353553 1
          0.0353553 0.0353553 0
          -0.0353553 0.0353553 0
          -0.0353553 0.0353553 1
          9.18455e-018 0.05 1
          9.18455e-018 0.05 0
          -0.05 0 0
          -0.05 0 1
          -0.0353553 0.0353553 1
          -0.0353553 0.0353553 0
          -0.05 0 1
          -0.0353553 -0.0353553 1
          -3.06152e-018 -0.05 1
          0.0353553 -0.0353553 1
          0.05 -6.12303e-018 1
          0.0353553 0.0353553 1
          9.18455e-018 0.05 1
          -0.0353553 0.0353553 1
        }
        NormalBinding PER_VERTEX
        NormalArray Vec3Array 40
        {
          -0.92388 -0.382683 -0
          -0.92388 -0.382683 -0
          -0.92388 -0.382683 -0
          -0.92388 -0.382683 -0
          -0.382683 -0.92388 -0
          -0.382683 -0.92388 -0
          -0.382683 -0.92388 -0
          -0.382683 -0.92388 -0
          0.382683 -0.92388 0
          0.382683 -0.92388 0
          0.382683 -0.92388 0
          0.382683 -0.92388 0
          0.92388 -0.382683 0
          0.92388 -0.382683 0
          0.92388 -0.382683 0
          0.92388 -0.382683 0
          0.92388 0.382683 0
          0.92388 0.382683 0
          0.92388 0.382683 0
          0.92388 0.382683 0
          0.382683 0.92388 0
          0.382683 0.92388 0
          0.382683 0.92388 0
          0.382683 0.92388 0
          -0.382683 0.92388 0
          -0.382683 0.92388 0
          -0.382683 0.92388 0
          -0.382683 0.92388 0
          -0.92388 0.382683 0
          -0.92388 0.382683 0
          -0.92388 0.382683 0
          -0.92388 0.382683 0
          0 -0 1
          0 -0 1
          0 -0 1
          0 -0 1
          0 -0 1
          0 -0 1
          0 -0 1
          0 -0 1
        }
        ColorBinding OVERALL
        ColorArray Vec4Array 1
        {
          1 1 1 1
        }
      }
    }
    Geode {
      DataVariance STATIC
      name "p31"
      nodeMask 0xffffffff
      cullingActive TRUE
      StateSet {
        DataVariance STATIC
        rendering_hint DEFAULT_BIN
        renderBinMode INHERIT
        GL_CULL_FACE OFF
      }
      num_drawables 1
      Geometry {
        DataVariance STATIC
        supportsDisplayList FALSE
        useDisplayList FALSE
        useVertexBufferObjects TRUE
        PrimitiveSets 1
        {
          DrawElementsUShort TRIANGLES 3
          {
            0 1 2 
          }
        }
        VertexArray Vec3Array 3
        {
          0.05 -6.12303e-018 0.80159
          0.3 0 0.9
          0.05 -6.12303e-018 1
        }
        NormalBinding PER_VERTEX
        NormalArray Vec3Array 3
        {
          2.44921e-017 -1 0
          2.44921e-017 -1 0
          2.44921e-017 -1 0
        }
        ColorBinding OVERALL
        ColorArray Vec4Array 1
        {
          1 0 0 1
        }
      }
    }
  }
}
